import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { getDatabaseUrl } from '../config';
import * as schema from './schema';

// 构建数据库连接字符串
const connectionString = getDatabaseUrl();

// 数据库连接配置
const connectionConfig = {
  max: 10, // 最大连接数
  idle_timeout: 30, // 空闲超时（秒）
  connect_timeout: 30, // 连接超时（秒）
  ssl: process.env.DB_SSL === 'true' ? 'require' : false,
};

// 创建数据库连接
const client = postgres(connectionString, connectionConfig);

// 创建 Drizzle 实例
export const db = drizzle(client, { 
  schema,
  logger: process.env.NODE_ENV === 'development',
});

// 数据库连接测试
export async function testConnection(): Promise<boolean> {
  try {
    await client`SELECT 1`;
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// 关闭数据库连接
export async function closeConnection(): Promise<void> {
  try {
    await client.end();
    console.log('🔌 Database connection closed');
  } catch (error) {
    console.error('❌ Error closing database connection:', error);
  }
}

// 数据库健康检查
export async function healthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  latency?: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    await client`SELECT 1`;
    const latency = Date.now() - startTime;
    
    return {
      status: 'healthy',
      latency,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 导出类型
export type Database = typeof db;
export { schema };
