# 开发环境配置
NODE_ENV=development

# 服务器配置
PORT=3000
HOST=0.0.0.0

# 数据库配置 - 开发环境
DB_HOST=localhost
DB_PORT=5432
DB_NAME=h3_dev_db
DB_USER=postgres
DB_PASSWORD=dev_password
DB_SSL=false

# 数据库连接池配置 (可选，有默认值)
# DB_POOL_MAX=20              # 最大连接数
# DB_POOL_MIN=2               # 最小连接数
# DB_IDLE_TIMEOUT=20          # 空闲超时(秒)
# DB_CONNECT_TIMEOUT=10       # 连接超时(秒)
# DB_MAX_LIFETIME=1800        # 连接最大生命周期(秒)

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=json
LOG_PRETTY_PRINT=true
LOG_TIMESTAMP=true
LOG_COLORIZE=true
DB_MAX_CONNECTIONS=5
DB_CONNECTION_TIMEOUT=30000

# Redis 配置 - 开发环境
REDIS_HOST=localhost
REDIS_PORT=6379
# REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=dev-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# CORS 配置 - 开发环境允许所有来源
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=pretty

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# API 配置
API_PREFIX=/api
API_VERSION=v1

# 安全配置 - 开发环境较宽松
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=1000

# 监控配置
HEALTH_CHECK_ENDPOINT=/health
METRICS_ENDPOINT=/metrics

# 开发工具
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
