{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "jsx": "preserve",
    "jsxImportSource": "solid-js",
    "allowJs": true,
    "strict": true,
    "noEmit": true,
    "skipLibCheck": true,
    "types": ["vinxi/types/client", "solid-js"],
    "isolatedModules": true,
    "paths": {
      "~/*": ["./src/*"]
    }
  },
  "include": [
    "src/**/*",
    // "src/types/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    ".vinxi",
    ".output"
  ]
}
