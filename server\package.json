{"name": "server_h3", "version": "1.0.0", "description": "H3 server with Bun runtime", "main": "src/index.ts", "module": "src/index.ts", "type": "module", "scripts": {"dev": "NODE_ENV=development bun run --watch src/index.ts", "dev:test": "NODE_ENV=test bun run --watch src/index.ts", "start": "NODE_ENV=production bun run src/index.ts", "start:dev": "NODE_ENV=development bun run src/index.ts", "start:test": "NODE_ENV=test bun run src/index.ts", "build": "bun build src/index.ts --outdir ./dist --target bun", "build:prod": "bun build src/index.ts --outdir ./dist --target bun --minify", "preview": "NODE_ENV=production bun run dist/index.js", "test": "NODE_ENV=test bun test", "test:watch": "NODE_ENV=test bun test --watch", "test:coverage": "NODE_ENV=test bun test --coverage", "db:generate": "drizzle-kit generate", "db:generate:dev": "NODE_ENV=development drizzle-kit generate", "db:generate:test": "NODE_ENV=test drizzle-kit generate", "db:generate:prod": "NODE_ENV=production drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:migrate:dev": "NODE_ENV=development drizzle-kit migrate", "db:migrate:test": "NODE_ENV=test drizzle-kit migrate", "db:migrate:prod": "NODE_ENV=production drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:studio:dev": "NODE_ENV=development drizzle-kit studio", "db:studio:test": "NODE_ENV=test drizzle-kit studio", "db:studio:prod": "NODE_ENV=production drizzle-kit studio", "db:push": "drizzle-kit push", "db:push:dev": "NODE_ENV=development drizzle-kit push", "db:push:test": "NODE_ENV=test drizzle-kit push", "db:push:prod": "NODE_ENV=production drizzle-kit push", "db:reset": "drizzle-kit drop && drizzle-kit push", "db:reset:dev": "NODE_ENV=development drizzle-kit drop && NODE_ENV=development drizzle-kit push", "db:reset:test": "NODE_ENV=test drizzle-kit drop && NODE_ENV=test drizzle-kit push", "db:reset:prod": "NODE_ENV=production drizzle-kit drop && NODE_ENV=production drizzle-kit push", "lint": "bun run --bun tsc --noEmit", "lint:fix": "bun run --bun tsc --noEmit --fix", "format": "bun x prettier --write .", "format:check": "bun x prettier --check .", "clean": "rm -rf dist node_modules/.cache", "docker:build": "docker build -f docker/Dockerfile -t server_h3 .", "docker:build:dev": "docker build -f docker/Dockerfile.dev -t server_h3:dev .", "docker:run": "docker run -p 3000:3000 server_h3", "docker:run:dev": "docker run -p 3000:3000 -v $(pwd):/app server_h3:dev", "docker:up": "cd docker && docker-compose up -d", "docker:up:dev": "cd docker && docker-compose -f docker-compose.dev.yml up -d", "docker:up:test": "cd docker && docker-compose -f docker-compose.test.yml up -d", "docker:down": "cd docker && docker-compose down", "docker:down:dev": "cd docker && docker-compose -f docker-compose.dev.yml down", "docker:down:test": "cd docker && docker-compose -f docker-compose.test.yml down", "docker:logs": "cd docker && docker-compose logs -f", "docker:logs:dev": "cd docker && docker-compose -f docker-compose.dev.yml logs -f", "docker:logs:test": "cd docker && docker-compose -f docker-compose.test.yml logs -f", "docker:restart": "cd docker && docker-compose restart", "install:clean": "rm -rf node_modules bun.lockb && bun install", "health": "curl -f http://localhost:3000/ || exit 1"}, "keywords": ["h3", "bun", "server", "api"], "author": "", "license": "MIT", "engines": {"bun": ">=1.0.0", "node": ">=20.11.0"}, "devDependencies": {"@types/bun": "latest", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.4", "tsx": "^4.20.3"}, "peerDependencies": {"typescript": "^5.7.3"}, "dependencies": {"@types/jsonwebtoken": "^9.0.10", "dotenv": "^16.6.1", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "h3": "^2.0.0-beta.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "pino": "^9.7.0", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.7", "zod": "^3.25.76"}}