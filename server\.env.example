# 环境配置
NODE_ENV=development

# 服务器配置
PORT=3000
HOST=0.0.0.0

# 数据库配置 (可以使用 DATABASE_URL 或单独配置)
# DATABASE_URL=postgresql://postgres:password@localhost:5432/h3_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=h3_db
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=30000

# Redis 配置 (可选)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production-must-be-at-least-32-characters
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d
JWT_ISSUER=h3-server
JWT_AUDIENCE=h3-client

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# CORS 配置
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# API 配置
API_PREFIX=/api
API_VERSION=v1

# 安全配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 监控配置
HEALTH_CHECK_ENDPOINT=/health
METRICS_ENDPOINT=/metrics
