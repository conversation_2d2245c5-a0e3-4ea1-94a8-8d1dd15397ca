version: '3.8'

services:
  # H3 生产服务器
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=h3_prod_db
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-secure_production_password}
      - DB_SSL=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - JWT_SECRET=${JWT_SECRET:-CHANGE_THIS_IN_PRODUCTION}
      - LOG_LEVEL=info
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - app-network

  # PostgreSQL 生产数据库
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=h3_prod_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${DB_PASSWORD:-secure_production_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - app-network
    command: postgres -c shared_preload_libraries=pg_stat_statements -c log_statement=all

  # Redis 生产缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
