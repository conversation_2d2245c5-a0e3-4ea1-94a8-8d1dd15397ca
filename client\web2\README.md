# H3 Template Web2 - SolidStart Frontend

基于 SolidStart 的现代化前端应用，与 H3 后端 API 集成。

## 🚀 **技术栈**

- **框架**: SolidStart (SolidJS 全栈框架)
- **样式**: Tailwind CSS
- **状态管理**: SolidJS Signals
- **路由**: SolidJS Router
- **构建工具**: Vinxi
- **包管理**: Bun
- **认证**: JWT + 自动刷新
- **API 客户端**: 自定义 Fetch 封装

## 🔧 **开发指南**

### 环境配置

1. 复制环境配置文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的配置：
```bash
# API 服务器配置
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=10000
```

### 安装依赖

```bash
bun install
```

### 启动开发服务器

```bash
bun run dev
```

应用将在 http://localhost:3001 启动

### 构建生产版本

```bash
bun run build
```

## This project was created with the [Solid CLI](https://github.com/solidjs-community/solid-cli)
