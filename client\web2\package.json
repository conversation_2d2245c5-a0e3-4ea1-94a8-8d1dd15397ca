{"name": "h3-template-web2", "version": "1.0.0", "description": "SolidStart frontend for H3 template", "type": "module", "scripts": {"dev": "vinxi dev --port 3001", "build": "vinxi build", "start": "vinxi start", "preview": "vinxi start", "version": "vinxi version", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@solidjs/meta": "^0.29.4", "@solidjs/router": "^0.15.0", "@solidjs/start": "^1.1.0", "solid-js": "^1.9.5", "vinxi": "^0.5.7"}, "devDependencies": {"@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-plugin-solid": "^0.14.0", "typescript": "^5.6.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "engines": {"node": ">=18"}}