# Bun 配置文件
[install]
# 使用更快的安装策略
cache = true
exact = false
production = false
optional = true
dev = true
peer = true

# 注册表配置
registry = "https://registry.npmjs.org"

[install.scopes]
# 可以配置特定作用域的注册表

[run]
# 运行时配置
bun = true
shell = "bun"

# 环境变量
[env]
NODE_ENV = "development"

[test]
# 测试配置
preload = ["./test/setup.ts"]
coverage = true

[build]
# 构建配置
target = "browser"
format = "esm"
splitting = true
minify = true
sourcemap = true
