# 测试环境配置
NODE_ENV=test

# 服务器配置
PORT=3001
HOST=127.0.0.1

# 数据库配置 - 测试环境 (独立数据库)
DB_HOST=localhost
DB_PORT=5433
DB_NAME=h3_test_db
DB_USER=test_user
DB_PASSWORD=test_password
DB_SSL=false
DB_MAX_CONNECTIONS=3
DB_CONNECTION_TIMEOUT=10000

# Redis 配置 - 测试环境
REDIS_HOST=localhost
REDIS_PORT=6380
# REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=test-jwt-secret-for-testing-only
JWT_EXPIRES_IN=1h

# CORS 配置 - 测试环境
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization

# 日志配置 - 测试环境静默模式
LOG_LEVEL=error
LOG_FORMAT=json

# 文件上传配置
MAX_FILE_SIZE=1048576
UPLOAD_DIR=./test-uploads

# API 配置
API_PREFIX=/api
API_VERSION=v1

# 安全配置 - 测试环境
RATE_LIMIT_WINDOW=1
RATE_LIMIT_MAX=1000

# 监控配置
HEALTH_CHECK_ENDPOINT=/health
METRICS_ENDPOINT=/metrics

# 测试工具
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=true
TEST_TIMEOUT=5000
