CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255),
	"phone" varchar(20),
	"name" varchar(255),
	"nickname" varchar(255),
	"avatar" text,
	"password_hash" varchar(255),
	"wechat_openid" varchar(255),
	"wechat_unionid" varchar(255),
	"wechat_nickname" varchar(255),
	"wechat_avatar" text,
	"wechat_gender" varchar(10),
	"wechat_city" varchar(100),
	"wechat_province" varchar(100),
	"wechat_country" varchar(100),
	"access_token" text,
	"refresh_token" text,
	"token_expires_at" timestamp with time zone,
	"is_active" boolean DEFAULT true,
	"is_email_verified" boolean DEFAULT false,
	"is_phone_verified" boolean DEFAULT false,
	"last_login_at" timestamp with time zone,
	"last_login_ip" varchar(45),
	"login_count" varchar(10) DEFAULT '0',
	"profile" jsonb,
	"preferences" jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "users_email_unique" UNIQUE("email"),
	CONSTRAINT "users_phone_unique" UNIQUE("phone"),
	CONSTRAINT "users_wechat_openid_unique" UNIQUE("wechat_openid"),
	CONSTRAINT "users_wechat_unionid_unique" UNIQUE("wechat_unionid")
);
