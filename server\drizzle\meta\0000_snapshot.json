{"id": "8cd1ba24-8769-4c8e-9be8-9800e2b65356", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "nickname": {"name": "nickname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "wechat_openid": {"name": "wechat_openid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "wechat_unionid": {"name": "wechat_unionid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "wechat_nickname": {"name": "wechat_nickname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "wechat_avatar": {"name": "wechat_avatar", "type": "text", "primaryKey": false, "notNull": false}, "wechat_gender": {"name": "wechat_gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "wechat_city": {"name": "wechat_city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "wechat_province": {"name": "wechat_province", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "wechat_country": {"name": "wechat_country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "token_expires_at": {"name": "token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_phone_verified": {"name": "is_phone_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "last_login_ip": {"name": "last_login_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "login_count": {"name": "login_count", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'0'"}, "profile": {"name": "profile", "type": "jsonb", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_unique": {"name": "users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}, "users_wechat_openid_unique": {"name": "users_wechat_openid_unique", "nullsNotDistinct": false, "columns": ["wechat_openid"]}, "users_wechat_unionid_unique": {"name": "users_wechat_unionid_unique", "nullsNotDistinct": false, "columns": ["wechat_unionid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}