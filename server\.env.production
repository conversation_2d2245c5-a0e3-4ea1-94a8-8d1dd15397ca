# 生产环境配置
NODE_ENV=production

# 服务器配置
PORT=3000
HOST=0.0.0.0

# 数据库配置 - 生产环境
# 生产环境建议使用 DATABASE_URL 环境变量
# DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require
DB_HOST=localhost
DB_PORT=5432
DB_NAME=h3_prod_db
DB_USER=postgres
DB_PASSWORD=secure_production_password
DB_SSL=true
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=30000

# Redis 配置 - 生产环境
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=secure_redis_password

# JWT 配置 - 生产环境必须使用强密钥
JWT_SECRET=CHANGE_THIS_TO_A_SECURE_RANDOM_STRING_IN_PRODUCTION
JWT_EXPIRES_IN=24h

# CORS 配置 - 生产环境限制来源
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization

# 日志配置 - 生产环境
LOG_LEVEL=info
LOG_FORMAT=json

# 文件上传配置
MAX_FILE_SIZE=5242880
UPLOAD_DIR=/app/uploads

# API 配置
API_PREFIX=/api
API_VERSION=v1

# 安全配置 - 生产环境严格限制
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 监控配置
HEALTH_CHECK_ENDPOINT=/health
METRICS_ENDPOINT=/metrics

# 生产环境安全
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=false

# SSL/TLS 配置
FORCE_HTTPS=true
TRUST_PROXY=true

# 会话配置
SESSION_SECRET=CHANGE_THIS_TO_A_SECURE_RANDOM_STRING
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
